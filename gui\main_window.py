"""
Main window for the Cold Emailer GUI application
"""
import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QVBoxLayout, QWidget,
    QMessageBox, QLabel, QStatusBar
)
from PyQt6.QtGui import QAction, QFont
from PyQt6.QtCore import Qt
import qtawesome as qta
from utils import create_default_dirs

# Import tab components
from gui.components.email_sender_tab import EmailSenderTab
from gui.components.contacts_tab import ContactsTab
from gui.components.templates_tab import TemplatesTab
from gui.components.settings_tab import SettingsTab

class MainWindow(QMainWindow):
    """Main window for the Cold Emailer GUI application"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Cold Emailer GUI")
        self.resize(1400, 900)  # Larger default size

        # Create default directories
        create_default_dirs()

        self.init_ui()
        
        # Set application icon
        self.setWindowIcon(qta.icon('fa5s.envelope'))
        
        # Create the central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create header
        header_layout = QVBoxLayout()
        header_label = QLabel("Cold Emailer GUI")
        header_label.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_label.setStyleSheet("color: #ffffff; margin: 10px 0;")
        header_layout.addWidget(header_label)

        # Add a subtitle
        subtitle_label = QLabel("Send personalized emails efficiently")
        subtitle_label.setFont(QFont("Segoe UI", 12))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #cccccc; margin-bottom: 20px;")
        header_layout.addWidget(subtitle_label)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tabs = QTabWidget()
        self.tabs.setFont(QFont("Segoe UI", 10))
        self.tabs.setTabPosition(QTabWidget.TabPosition.North)
        self.tabs.setDocumentMode(True)
        
        # Create tabs
        self.email_sender_tab = EmailSenderTab()
        self.contacts_tab = ContactsTab()
        self.templates_tab = TemplatesTab()
        self.settings_tab = SettingsTab()
        
        # Add tabs to tab widget with icons
        self.tabs.addTab(self.email_sender_tab, qta.icon('fa5s.paper-plane'), "Send Emails")
        self.tabs.addTab(self.contacts_tab, qta.icon('fa5s.address-book'), "Contacts")
        self.tabs.addTab(self.templates_tab, qta.icon('fa5s.file-alt'), "Templates")
        self.tabs.addTab(self.settings_tab, qta.icon('fa5s.cog'), "Settings")
        
        main_layout.addWidget(self.tabs)
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
        
        # Set up menu bar
        self.setup_menu()
        
        # Apply stylesheet
        self.apply_stylesheet()
    
    def init_ui(self):
        self.setMinimumSize(1200, 800)  # Larger minimum size
    
    def setup_menu(self):
        """Set up the application menu bar"""
        menu_bar = self.menuBar()
        
        # File menu
        file_menu = menu_bar.addMenu("&File")
        
        # Exit action
        exit_action = QAction(qta.icon('fa5s.sign-out-alt'), "&Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menu_bar.addMenu("&Help")
        
        # About action
        about_action = QAction(qta.icon('fa5s.info-circle'), "&About", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
        
        # Documentation action
        docs_action = QAction(qta.icon('fa5s.book'), "&Documentation", self)
        docs_action.triggered.connect(self.show_documentation)
        help_menu.addAction(docs_action)
    
    def show_about_dialog(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Cold Emailer GUI",
            "Cold Emailer GUI v1.0.0\n\n"
            "A modern GUI application for sending personalized cold emails.\n\n"
            "2023-2025 Cold Emailer GUI"
        )
    
    def show_documentation(self):
        """Show documentation dialog"""
        QMessageBox.information(
            self,
            "Documentation",
            "Cold Emailer GUI Documentation\n\n"
            "For detailed documentation, please visit:\n"
            "https://github.com/AndrxwWxng/cold-emailer\n\n"
            "Quick Start:\n"
            "1. Configure your email settings in the Settings tab\n"
            "2. Create or import contacts in the Contacts tab\n"
            "3. Create email templates in the Templates tab\n"
            "4. Send emails using the Send Emails tab"
        )
    
    def apply_stylesheet(self):
        """Apply a modern dark theme stylesheet to the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            QWidget {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #333333;
                border-radius: 8px;
                background-color: #2d2d2d;
            }
            QTabBar::tab {
                background-color: transparent;
                border: none;
                padding: 12px 24px;
                margin-right: 4px;
                color: #cccccc;
                font-weight: 500;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background-color: #4361ee;
                border-radius: 6px;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #404040;
                border-radius: 6px;
                color: #ffffff;
            }
            QPushButton {
                background-color: #4361ee;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 500;
                min-height: 40px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3a56d4;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #2c4bc7;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #666666;
            }
            QPushButton#test_button {
                background-color: #20c997;
            }
            QPushButton#test_button:hover {
                background-color: #1db386;
            }
            QPushButton#test_button:pressed {
                background-color: #199473;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox {
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 12px;
                background-color: #333333;
                color: #ffffff;
                min-height: 28px;
                font-size: 14px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
                border: 2px solid #4361ee;
                background-color: #404040;
            }
            QLineEdit:read-only {
                background-color: #2a2a2a;
                color: #cccccc;
            }
            QLabel {
                color: #ffffff;
                font-weight: 400;
                font-size: 14px;
            }
            QGroupBox {
                border: 1px solid #555555;
                border-radius: 12px;
                margin-top: 20px;
                padding-top: 20px;
                font-weight: 600;
                background-color: #2d2d2d;
                font-size: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #4361ee;
                font-weight: 600;
            }
            QProgressBar {
                border: none;
                border-radius: 6px;
                background-color: #404040;
                text-align: center;
                min-height: 16px;
                color: #ffffff;
                font-weight: 500;
            }
            QProgressBar::chunk {
                background-color: #4361ee;
                border-radius: 6px;
            }
            QCheckBox {
                spacing: 10px;
                color: #ffffff;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 6px;
                border: 2px solid #555555;
                background-color: #333333;
            }
            QCheckBox::indicator:checked {
                background-color: #4361ee;
                border: 2px solid #4361ee;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #4361ee;
            }
            QScrollBar:vertical {
                border: none;
                background: #2d2d2d;
                width: 12px;
                margin: 0px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #555555;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical:hover {
                background: #666666;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #4361ee;
                border-radius: 4px;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #ffffff;
                border-top: none;
                border-left: none;
                width: 6px;
                height: 6px;
                transform: rotate(45deg);
            }
            QListWidget {
                background-color: #333333;
                border: 1px solid #555555;
                border-radius: 8px;
                color: #ffffff;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
            }
            QListWidget::item:selected {
                background-color: #4361ee;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #404040;
            }
            QTableWidget {
                background-color: #333333;
                border: 1px solid #555555;
                border-radius: 8px;
                color: #ffffff;
                gridline-color: #404040;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
            }
            QTableWidget::item:selected {
                background-color: #4361ee;
                color: white;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: 600;
            }
            QStatusBar {
                background-color: #2d2d2d;
                color: #ffffff;
                border-top: 1px solid #555555;
            }
            QMenuBar {
                background-color: #2d2d2d;
                color: #ffffff;
                border-bottom: 1px solid #555555;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
            }
            QMenuBar::item:selected {
                background-color: #4361ee;
                border-radius: 4px;
            }
            QMenu {
                background-color: #333333;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 8px;
            }
            QMenu::item {
                padding: 8px 16px;
            }
            QMenu::item:selected {
                background-color: #4361ee;
            }
        """)

def main():
    """Main entry point for the GUI application"""
    app = QApplication(sys.argv)
    app.setStyle("Fusion")  
    
    app.setApplicationName("Cold Emailer GUI")
    app.setOrganizationName("Andrew Wang")
    
    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
