"""
Utility functions for the Cold Emailer GUI application
"""
import os
import sys
import json
import logging
from datetime import datetime
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('cold_emailer_gui')

def load_config():
    """Load configuration from config.json file"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def load_environment_variables():
    """Load environment variables from .env file"""
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    load_dotenv(env_path)

    # Check for required environment variables
    required_vars = ['EMAIL_ADDRESS', 'EMAIL_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        logger.warning(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    return True

def get_templates_dir():
    """Get the path to the templates directory"""
    config = load_config()
    templates_dir = config.get('templates', {}).get('templates_dir', 'templates')

    # Make sure it's an absolute path
    if not os.path.isabs(templates_dir):
        templates_dir = os.path.join(os.path.dirname(__file__), templates_dir)

    # Create directory if it doesn't exist
    os.makedirs(templates_dir, exist_ok=True)

    return templates_dir

def get_contacts_dir():
    """Get the path to the contacts directory"""
    config = load_config()
    contacts_dir = config.get('contacts', {}).get('default_file', 'contacts')

    # If it's a file path, get the directory
    if os.path.splitext(contacts_dir)[1]:
        contacts_dir = os.path.dirname(contacts_dir)

    # Make sure it's an absolute path
    if not os.path.isabs(contacts_dir):
        contacts_dir = os.path.join(os.path.dirname(__file__), contacts_dir)

    # Create directory if it doesn't exist
    os.makedirs(contacts_dir, exist_ok=True)

    return contacts_dir

def format_timestamp(timestamp=None):
    """Format a timestamp for display"""
    if timestamp is None:
        timestamp = datetime.now()
    elif isinstance(timestamp, str):
        try:
            timestamp = datetime.fromisoformat(timestamp)
        except ValueError:
            return timestamp
    
    return timestamp.strftime('%Y-%m-%d %H:%M:%S')

def get_app_icon_path():
    """Get the path to the application icon"""
    return os.path.join(os.path.dirname(__file__), 'resources', 'icon.png')

def create_default_dirs():
    """Create default directories for the application"""
    # Create logs directory
    logs_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.makedirs(logs_dir, exist_ok=True)

    # Create backups directory
    backups_dir = os.path.join(os.path.dirname(__file__), 'backups')
    os.makedirs(backups_dir, exist_ok=True)

    # Create templates directory
    templates_dir = get_templates_dir()
    os.makedirs(templates_dir, exist_ok=True)

    # Create contacts directory
    contacts_dir = get_contacts_dir()
    os.makedirs(contacts_dir, exist_ok=True)

    return {
        'logs_dir': logs_dir,
        'backups_dir': backups_dir,
        'templates_dir': templates_dir,
        'contacts_dir': contacts_dir
    }
