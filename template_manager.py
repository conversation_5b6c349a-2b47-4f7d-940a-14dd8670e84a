"""
Template manager module for Cold Emailer GUI
"""
import os
import json
import logging
from datetime import datetime

logger = logging.getLogger('template_manager')

class TemplateManager:
    """Class for managing email templates"""
    
    def __init__(self, templates_dir='templates'):
        """Initialize the template manager"""
        self.templates_dir = templates_dir
        
        # Create templates directory if it doesn't exist
        os.makedirs(self.templates_dir, exist_ok=True)
    
    def get_templates(self):
        """Get all available templates"""
        templates = []
        
        # Check if templates directory exists
        if not os.path.exists(self.templates_dir):
            logger.warning(f"Templates directory not found: {self.templates_dir}")
            return templates
        
        # Get all JSON files in the templates directory
        for filename in os.listdir(self.templates_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(self.templates_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        template = json.load(f)
                        # Add filename without extension as ID if not present
                        if 'id' not in template:
                            template['id'] = os.path.splitext(filename)[0]
                        templates.append(template)
                except Exception as e:
                    logger.error(f"Error loading template {filename}: {e}")
        
        return templates
    
    def get_template(self, template_id):
        """Get a specific template by ID"""
        file_path = os.path.join(self.templates_dir, f"{template_id}.json")
        
        if not os.path.exists(file_path):
            logger.error(f"Template not found: {template_id}")
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                template = json.load(f)
                # Add ID if not present
                if 'id' not in template:
                    template['id'] = template_id
                return template
        except Exception as e:
            logger.error(f"Error loading template {template_id}: {e}")
            return None
    
    def save_template(self, template):
        """Save a template"""
        # Make sure template has an ID
        if 'id' not in template or not template['id']:
            logger.error("Template must have an ID")
            return False
        
        # Add timestamp
        template['updated'] = datetime.now().isoformat()
        
        # Save template to file
        file_path = os.path.join(self.templates_dir, f"{template['id']}.json")
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2)
            logger.info(f"Template saved: {template['id']}")
            return True
        except Exception as e:
            logger.error(f"Error saving template {template['id']}: {e}")
            return False
    
    def delete_template(self, template_id):
        """Delete a template"""
        file_path = os.path.join(self.templates_dir, f"{template_id}.json")
        
        if not os.path.exists(file_path):
            logger.error(f"Template not found: {template_id}")
            return False
        
        try:
            os.remove(file_path)
            logger.info(f"Template deleted: {template_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting template {template_id}: {e}")
            return False
    
    def create_sample_template(self, template_id='sample'):
        """Create a sample template"""
        sample_template = {
            'id': template_id,
            'name': 'Sample Template',
            'description': 'A sample email template',
            'subject': 'Hello {first_name}, opportunity to connect',
            'body': """Dear {first_name},

I hope this email finds you well. I recently came across your work at {company} and I was impressed by your contributions.

I would love to connect and discuss potential opportunities to collaborate.

Best regards,
Your Name
""",
            'tags': ['sample', 'general'],
            'created': datetime.now().isoformat(),
            'updated': datetime.now().isoformat()
        }
        
        return self.save_template(sample_template)
    
    def create_template(self, template_name, subject, body, description=""):
        """Create a new template"""
        template = {
            'id': template_name,
            'name': template_name,
            'description': description,
            'subject': subject,
            'body': body,
            'created': datetime.now().isoformat(),
            'updated': datetime.now().isoformat()
        }
        return self.save_template(template)

    def update_template(self, template_name, subject, body, description=""):
        """Update an existing template"""
        template = {
            'id': template_name,
            'name': template_name,
            'description': description,
            'subject': subject,
            'body': body,
            'updated': datetime.now().isoformat()
        }
        return self.save_template(template)

    def get_variable_help(self):
        """Get help text for template variables"""
        return {
            'first_name': 'First name of the recipient',
            'last_name': 'Last name of the recipient',
            'full_name': 'Full name of the recipient (first + last)',
            'email': 'Email address of the recipient',
            'company': 'Company name of the recipient',
            'position': 'Job position of the recipient'
        }
